<?php

use App\Http\Controllers\ContactController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ToolController;
use Illuminate\Support\Facades\Route;

// Home page
Route::get('/', [HomeController::class, 'index'])->name('home');

// Tool pages
Route::get('/tools/case-converter', [ToolController::class, 'caseConverter'])->name('tools.case-converter');
Route::get('/tools/word-counter', [ToolController::class, 'wordCounter'])->name('tools.word-counter');
Route::get('/tools/age-calculator', [ToolController::class, 'ageCalculator'])->name('tools.age-calculator');

// About page
Route::get('/about', [HomeController::class, 'about'])->name('about');

// Contact page
Route::get('/contact', [ContactController::class, 'index'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

// SEO routes
Route::get('/sitemap.xml', [HomeController::class, 'sitemap'])->name('sitemap');
