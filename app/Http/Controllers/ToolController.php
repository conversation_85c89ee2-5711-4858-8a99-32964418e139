<?php

namespace App\Http\Controllers;

class ToolController extends Controller
{
    /**
     * Display the Case Converter tool
     */
    public function caseConverter()
    {
        $tool = [
            'name' => 'Case Converter',
            'description' => 'Convert text between different case formats instantly. Perfect for formatting text for different purposes.',
            'meta_description' => 'Free online case converter tool. Convert text to uppercase, lowercase, title case, sentence case, and more formats instantly.',
            'keywords' => 'case converter, text converter, uppercase, lowercase, title case, sentence case, text formatting',
        ];

        return view('tools.case-converter', compact('tool'));
    }

    /**
     * Display the Word Counter tool
     */
    public function wordCounter()
    {
        $tool = [
            'name' => 'Word & Character Counter',
            'description' => 'Count words, characters, paragraphs, and sentences in real-time. Get reading time estimates and detailed text statistics.',
            'meta_description' => 'Free online word and character counter tool. Real-time counting with reading time estimates and detailed text statistics.',
            'keywords' => 'word counter, character counter, text counter, word count, character count, reading time, text statistics',
        ];

        return view('tools.word-counter', compact('tool'));
    }

    /**
     * Display the Age Calculator tool
     */
    public function ageCalculator()
    {
        $tool = [
            'name' => 'Age Calculator',
            'description' => 'Calculate your exact age in years, months, and days. Find out how many days you\'ve lived and when your next birthday is.',
            'meta_description' => 'Free online age calculator. Calculate exact age in years, months, days. Find days lived, next birthday, and zodiac sign.',
            'keywords' => 'age calculator, calculate age, birth date calculator, age in days, age in months, birthday calculator',
        ];

        return view('tools.age-calculator', compact('tool'));
    }
}
