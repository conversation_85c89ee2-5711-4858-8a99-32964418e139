<?php

namespace App\Http\Controllers;

class HomeController extends Controller
{
    /**
     * Display the home page with tool showcase
     */
    public function index()
    {
        $tools = [
            [
                'name' => 'Case Converter',
                'description' => 'Convert text between uppercase, lowercase, title case, and more formats instantly.',
                'icon' => 'case-converter',
                'route' => 'tools.case-converter',
                'features' => ['Uppercase', 'Lowercase', 'Title Case', 'Sentence Case'],
            ],
            [
                'name' => 'Word & Character Counter',
                'description' => 'Real-time word and character counting as you type or paste text.',
                'icon' => 'word-counter',
                'route' => 'tools.word-counter',
                'features' => ['Word Count', 'Character Count', 'Real-time Updates', 'Reading Time'],
            ],
            [
                'name' => 'Age Calculator',
                'description' => 'Calculate precise age in years, months, and days from any birth date.',
                'icon' => 'age-calculator',
                'route' => 'tools.age-calculator',
                'features' => ['Exact Age', 'Days Lived', 'Next Birthday', 'Zodiac Sign'],
            ],
        ];

        $faqs = [
            [
                'question' => 'Are these tools free to use?',
                'answer' => 'Yes, all our tools are completely free to use with no registration required.',
            ],
            [
                'question' => 'Do you store my data?',
                'answer' => 'No, all processing happens in your browser. We don\'t store or transmit your data to our servers.',
            ],
            [
                'question' => 'Can I use these tools offline?',
                'answer' => 'Once loaded, most tools work offline as they run entirely in your browser.',
            ],
            [
                'question' => 'Are the tools mobile-friendly?',
                'answer' => 'Yes, all our tools are designed to work perfectly on mobile devices and tablets.',
            ],
        ];

        return view('home', compact('tools', 'faqs'));
    }

    /**
     * Display the about page
     */
    public function about()
    {
        return view('about');
    }

    /**
     * Generate XML sitemap
     */
    public function sitemap()
    {
        $urls = [
            [
                'url' => route('home'),
                'lastmod' => now()->toDateString(),
                'changefreq' => 'weekly',
                'priority' => '1.0',
            ],
            [
                'url' => route('tools.case-converter'),
                'lastmod' => now()->toDateString(),
                'changefreq' => 'monthly',
                'priority' => '0.9',
            ],
            [
                'url' => route('tools.word-counter'),
                'lastmod' => now()->toDateString(),
                'changefreq' => 'monthly',
                'priority' => '0.9',
            ],
            [
                'url' => route('tools.age-calculator'),
                'lastmod' => now()->toDateString(),
                'changefreq' => 'monthly',
                'priority' => '0.9',
            ],
            [
                'url' => route('about'),
                'lastmod' => now()->toDateString(),
                'changefreq' => 'monthly',
                'priority' => '0.7',
            ],
            [
                'url' => route('contact'),
                'lastmod' => now()->toDateString(),
                'changefreq' => 'monthly',
                'priority' => '0.6',
            ],
        ];

        return response()->view('sitemap', compact('urls'))
            ->header('Content-Type', 'application/xml');
    }
}
