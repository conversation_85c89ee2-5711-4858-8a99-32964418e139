# Unitlly - Free Online Tools

A comprehensive collection of free, fast, and secure online tools built with Laravel 12 and Tailwind CSS 4.0.

## 🚀 Features

### Core Tools
- **Case Converter**: Transform text between uppercase, lowercase, title case, sentence case, camelCase, PascalCase, snake_case, and kebab-case
- **Word & Character Counter**: Real-time text analysis with word count, character count, reading time estimates, and detailed statistics
- **Age Calculator**: Calculate precise age in years, months, and days with additional insights like zodiac signs and next birthday countdown

### Technical Features
- **Mobile-First Responsive Design**: Optimized for all devices and screen sizes
- **SEO Optimized**: Complete meta tags, structured data, and sitemap generation
- **Privacy-First**: All processing happens in the browser - no data is stored or transmitted
- **Lightning Fast**: Instant results with optimized performance
- **Accessibility**: WCAG compliant with keyboard navigation and screen reader support
- **Google Ads Ready**: Pre-configured ad placement sections

## 🛠 Tech Stack

- **Backend**: Laravel 12 (PHP 8.2+)
- **Frontend**: Blade Templates + Tailwind CSS 4.0
- **JavaScript**: Vanilla JS with modern browser APIs
- **Build Tools**: Vite
- **Fonts**: Inter (Google Fonts)

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd unitlly.com
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install Node.js dependencies**
   ```bash
   npm install
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Build assets**
   ```bash
   npm run build
   # or for development
   npm run dev
   ```

6. **Start the development server**
   ```bash
   php artisan serve
   ```

## 🏗 Project Structure

```
├── app/
│   └── Http/Controllers/
│       ├── HomeController.php      # Homepage and sitemap
│       ├── ToolController.php      # Tool pages
│       └── ContactController.php   # Contact form
├── resources/
│   ├── views/
│   │   ├── layouts/
│   │   │   └── app.blade.php      # Main layout template
│   │   ├── tools/                 # Individual tool pages
│   │   ├── home.blade.php         # Homepage
│   │   ├── about.blade.php        # About page
│   │   ├── contact.blade.php      # Contact page
│   │   └── sitemap.blade.php      # XML sitemap
│   ├── css/
│   │   └── app.css               # Tailwind CSS + custom styles
│   └── js/
│       └── app.js                # JavaScript utilities
├── routes/
│   └── web.php                   # Application routes
└── public/
    └── robots.txt               # SEO robots file
```

## 🎨 Design System

### Colors
- **Primary**: Blue gradient (blue-600 to purple-600)
- **Secondary**: Individual tool colors (green, purple, red)
- **Neutral**: Gray scale for text and backgrounds
- **Status**: Green (success), Red (error), Yellow (warning)

### Typography
- **Font**: Inter (400, 500, 600, 700 weights)
- **Headings**: Bold weights with proper hierarchy
- **Body**: Regular weight with good contrast ratios

### Components
- **Cards**: Rounded corners with subtle shadows
- **Buttons**: Gradient backgrounds with hover effects
- **Forms**: Clean inputs with focus states
- **Navigation**: Sticky header with dropdown menus

## 🔧 Configuration

### Google AdSense
Replace the placeholder AdSense code in `resources/views/layouts/app.blade.php`:
```html
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-YOUR_PUBLISHER_ID" crossorigin="anonymous"></script>
```

### Contact Form
Configure email settings in `.env`:
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Unitlly"
```

### SEO Settings
Update the sitemap URL in `public/robots.txt` to match your domain:
```
Sitemap: https://yourdomain.com/sitemap.xml
```

## 📱 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🚀 Deployment

### Production Build
```bash
npm run build
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### Server Requirements
- PHP 8.2 or higher
- Composer
- Node.js 18+ (for building assets)
- Web server (Apache/Nginx)

### Recommended Server Configuration
```nginx
# Nginx configuration example
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/unitlly.com/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}
```

## 🔒 Security Features

- CSRF protection on all forms
- XSS protection with proper output escaping
- Content Security Policy headers
- Input validation and sanitization
- No data storage (privacy-first approach)

## 📊 Performance Features

- Optimized asset bundling with Vite
- Lazy loading for images
- Efficient CSS with Tailwind's purging
- Minimal JavaScript footprint
- Browser caching headers
- Compressed assets

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- Laravel framework for the robust backend
- Tailwind CSS for the utility-first styling
- Inter font family for beautiful typography
- The open-source community for inspiration

## 📞 Support

For support, email <EMAIL> or create an issue in the repository.

---

**Built with ❤️ for productivity and efficiency**