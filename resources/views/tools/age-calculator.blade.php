@extends('layouts.app')

@section('title', $tool['name'] . ' - Free Online Age Calculator Tool - Unitlly')
@section('meta_description', $tool['meta_description'])
@section('keywords', $tool['keywords'])

@section('content')
<!-- Tool Header -->
<section class="bg-gradient-to-br from-purple-50 to-indigo-100 py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center mx-auto mb-6">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
        </div>
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">{{ $tool['name'] }}</h1>
        <p class="text-xl text-gray-600 max-w-2xl mx-auto">{{ $tool['description'] }}</p>
    </div>
</section>

<!-- Tool Interface -->
<section class="py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <!-- Input Section -->
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Enter Your Birth Date</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="birth-day" class="block text-sm font-medium text-gray-700 mb-1">Day</label>
                        <select id="birth-day" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                            <option value="">Select Day</option>
                            <!-- Days will be populated by JavaScript -->
                        </select>
                    </div>
                    <div>
                        <label for="birth-month" class="block text-sm font-medium text-gray-700 mb-1">Month</label>
                        <select id="birth-month" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                            <option value="">Select Month</option>
                            <option value="0">January</option>
                            <option value="1">February</option>
                            <option value="2">March</option>
                            <option value="3">April</option>
                            <option value="4">May</option>
                            <option value="5">June</option>
                            <option value="6">July</option>
                            <option value="7">August</option>
                            <option value="8">September</option>
                            <option value="9">October</option>
                            <option value="10">November</option>
                            <option value="11">December</option>
                        </select>
                    </div>
                    <div>
                        <label for="birth-year" class="block text-sm font-medium text-gray-700 mb-1">Year</label>
                        <select id="birth-year" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                            <option value="">Select Year</option>
                            <!-- Years will be populated by JavaScript -->
                        </select>
                    </div>
                </div>
                
                <div class="mt-6">
                    <button id="calculate-age" class="w-full md:w-auto px-8 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-medium rounded-md hover:from-purple-700 hover:to-indigo-700 transition-colors">
                        Calculate My Age
                    </button>
                </div>
            </div>

            <!-- Results Section -->
            <div id="results-section" class="p-6 hidden">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Your Age Details</h3>
                
                <!-- Main Age Display -->
                <div class="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-lg p-6 mb-6">
                    <div class="text-center">
                        <div class="text-4xl font-bold text-purple-600 mb-2" id="main-age">25 years old</div>
                        <div class="text-gray-600" id="birth-date-display">Born on January 15, 1998</div>
                    </div>
                </div>

                <!-- Detailed Age Breakdown -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-blue-600" id="years">0</div>
                        <div class="text-sm text-gray-600">Years</div>
                    </div>
                    <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-green-600" id="months">0</div>
                        <div class="text-sm text-gray-600">Months</div>
                    </div>
                    <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-red-600" id="days">0</div>
                        <div class="text-sm text-gray-600">Days</div>
                    </div>
                </div>

                <!-- Additional Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <h4 class="font-medium text-gray-900">Time Lived</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Total days:</span>
                                <span id="total-days" class="font-medium">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Total hours:</span>
                                <span id="total-hours" class="font-medium">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Total minutes:</span>
                                <span id="total-minutes" class="font-medium">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Total seconds:</span>
                                <span id="total-seconds" class="font-medium">0</span>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <h4 class="font-medium text-gray-900">Next Milestones</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Next birthday:</span>
                                <span id="next-birthday" class="font-medium">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Days until birthday:</span>
                                <span id="days-until-birthday" class="font-medium">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Zodiac sign:</span>
                                <span id="zodiac-sign" class="font-medium">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Day of week born:</span>
                                <span id="day-of-week-born" class="font-medium">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Google Ad Section -->
<section class="py-8 bg-gray-100">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <p class="text-sm text-gray-500 mb-4">Advertisement</p>
            <!-- Google Ad Placeholder -->
            <div class="bg-white border-2 border-dashed border-gray-300 rounded-lg p-8">
                <p class="text-gray-400">Google Ad Space (728x90)</p>
                <!-- Replace with actual Google Ad code -->
                <ins class="adsbygoogle"
                     style="display:inline-block;width:728px;height:90px"
                     data-ad-client="ca-pub-XXXXXXXXXX"
                     data-ad-slot="XXXXXXXXXX"></ins>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-16 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-8 text-center">Age Calculator Features</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900">Precise Calculations</h3>
                    <p class="text-gray-600">Get your exact age in years, months, and days with accurate calculations.</p>
                </div>
            </div>

            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900">Detailed Statistics</h3>
                    <p class="text-gray-600">See how many days, hours, minutes, and seconds you've been alive.</p>
                </div>
            </div>

            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900">Next Birthday Countdown</h3>
                    <p class="text-gray-600">Find out exactly when your next birthday is and how many days until then.</p>
                </div>
            </div>

            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900">Zodiac & Fun Facts</h3>
                    <p class="text-gray-600">Discover your zodiac sign and what day of the week you were born on.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Other Tools Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-8 text-center">Other Useful Tools</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <a href="{{ route('tools.case-converter') }}" class="block p-6 bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Case Converter</h3>
                        <p class="text-gray-600">Convert text between different case formats</p>
                    </div>
                </div>
            </a>

            <a href="{{ route('tools.word-counter') }}" class="block p-6 bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Word Counter</h3>
                        <p class="text-gray-600">Count words and characters in real-time</p>
                    </div>
                </div>
            </a>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const birthDay = document.getElementById('birth-day');
    const birthMonth = document.getElementById('birth-month');
    const birthYear = document.getElementById('birth-year');
    const calculateButton = document.getElementById('calculate-age');
    const resultsSection = document.getElementById('results-section');

    // Populate days (1-31)
    for (let i = 1; i <= 31; i++) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = i;
        birthDay.appendChild(option);
    }

    // Populate years (current year - 120 to current year)
    const currentYear = new Date().getFullYear();
    for (let i = currentYear; i >= currentYear - 120; i--) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = i;
        birthYear.appendChild(option);
    }

    // Zodiac signs
    const zodiacSigns = [
        { name: 'Capricorn', start: [12, 22], end: [1, 19] },
        { name: 'Aquarius', start: [1, 20], end: [2, 18] },
        { name: 'Pisces', start: [2, 19], end: [3, 20] },
        { name: 'Aries', start: [3, 21], end: [4, 19] },
        { name: 'Taurus', start: [4, 20], end: [5, 20] },
        { name: 'Gemini', start: [5, 21], end: [6, 20] },
        { name: 'Cancer', start: [6, 21], end: [7, 22] },
        { name: 'Leo', start: [7, 23], end: [8, 22] },
        { name: 'Virgo', start: [8, 23], end: [9, 22] },
        { name: 'Libra', start: [9, 23], end: [10, 22] },
        { name: 'Scorpio', start: [10, 23], end: [11, 21] },
        { name: 'Sagittarius', start: [11, 22], end: [12, 21] }
    ];

    function getZodiacSign(month, day) {
        for (const sign of zodiacSigns) {
            if ((month === sign.start[0] && day >= sign.start[1]) || 
                (month === sign.end[0] && day <= sign.end[1])) {
                return sign.name;
            }
        }
        return 'Capricorn'; // Default fallback
    }

    function formatNumber(num) {
        return num.toLocaleString();
    }

    function calculateAge() {
        const day = parseInt(birthDay.value);
        const month = parseInt(birthMonth.value);
        const year = parseInt(birthYear.value);

        if (!day || month === '' || !year) {
            alert('Please select your complete birth date.');
            return;
        }

        const birthDate = new Date(year, month, day);
        const today = new Date();

        // Validate birth date
        if (birthDate > today) {
            alert('Birth date cannot be in the future.');
            return;
        }

        // Calculate age
        let ageYears = today.getFullYear() - birthDate.getFullYear();
        let ageMonths = today.getMonth() - birthDate.getMonth();
        let ageDays = today.getDate() - birthDate.getDate();

        if (ageDays < 0) {
            ageMonths--;
            const lastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
            ageDays += lastMonth.getDate();
        }

        if (ageMonths < 0) {
            ageYears--;
            ageMonths += 12;
        }

        // Calculate total time lived
        const timeDiff = today.getTime() - birthDate.getTime();
        const totalDays = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        const totalHours = Math.floor(timeDiff / (1000 * 60 * 60));
        const totalMinutes = Math.floor(timeDiff / (1000 * 60));
        const totalSeconds = Math.floor(timeDiff / 1000);

        // Calculate next birthday
        const nextBirthday = new Date(today.getFullYear(), month, day);
        if (nextBirthday < today) {
            nextBirthday.setFullYear(today.getFullYear() + 1);
        }
        const daysUntilBirthday = Math.ceil((nextBirthday.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

        // Get zodiac sign
        const zodiac = getZodiacSign(month + 1, day);

        // Get day of week born
        const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const dayOfWeekBorn = daysOfWeek[birthDate.getDay()];

        // Update display
        document.getElementById('main-age').textContent = `${ageYears} years old`;
        document.getElementById('birth-date-display').textContent = `Born on ${birthDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}`;
        
        document.getElementById('years').textContent = ageYears;
        document.getElementById('months').textContent = ageMonths;
        document.getElementById('days').textContent = ageDays;
        
        document.getElementById('total-days').textContent = formatNumber(totalDays);
        document.getElementById('total-hours').textContent = formatNumber(totalHours);
        document.getElementById('total-minutes').textContent = formatNumber(totalMinutes);
        document.getElementById('total-seconds').textContent = formatNumber(totalSeconds);
        
        document.getElementById('next-birthday').textContent = nextBirthday.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
        document.getElementById('days-until-birthday').textContent = daysUntilBirthday;
        document.getElementById('zodiac-sign').textContent = zodiac;
        document.getElementById('day-of-week-born').textContent = dayOfWeekBorn;

        // Show results
        resultsSection.classList.remove('hidden');
        resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    calculateButton.addEventListener('click', calculateAge);

    // Allow Enter key to calculate
    [birthDay, birthMonth, birthYear].forEach(element => {
        element.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                calculateAge();
            }
        });
    });
});
</script>
@endpush
