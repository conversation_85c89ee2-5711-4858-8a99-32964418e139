@extends('layouts.app')

@section('title', $tool['name'] . ' - Free Online Case Converter Tool - Unitlly')
@section('meta_description', $tool['meta_description'])
@section('keywords', $tool['keywords'])

@section('content')
    <!-- Tool Header -->
    <section class="bg-gradient-to-br from-blue-50 to-indigo-100 py-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-6">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                </svg>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">{{ $tool['name'] }}</h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">{{ $tool['description'] }}</p>
        </div>
    </section>

    <!-- Tool Interface -->
    <section class="py-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                <!-- Input Section -->
                <div class="p-6 border-b border-gray-200">
                    <label for="input-text" class="block text-sm font-medium text-gray-700 mb-2">
                        Enter your text below:
                    </label>
                    <textarea
                        id="input-text"
                        rows="8"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                        placeholder="Type or paste your text here..."
                    >The Quick Brown Fox Jumps Over The Lazy Dog</textarea>

                    <div class="flex justify-between items-center mt-3 text-sm text-gray-500">
                        <span>Sample text loaded - replace with your own</span>
                        <button id="clear-text" class="text-red-600 hover:text-red-700 font-medium">Clear</button>
                    </div>
                </div>

                <!-- Conversion Buttons -->
                <div class="p-6 bg-gray-50 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Choose conversion type:</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <button class="conversion-btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors" data-type="uppercase">
                            UPPERCASE
                        </button>
                        <button class="conversion-btn bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium transition-colors" data-type="lowercase">
                            lowercase
                        </button>
                        <button class="conversion-btn bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md font-medium transition-colors" data-type="titlecase">
                            Title Case
                        </button>
                        <button class="conversion-btn bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md font-medium transition-colors" data-type="sentencecase">
                            Sentence case
                        </button>
                        <button class="conversion-btn bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-md font-medium transition-colors" data-type="camelcase">
                            camelCase
                        </button>
                        <button class="conversion-btn bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md font-medium transition-colors" data-type="pascalcase">
                            PascalCase
                        </button>
                        <button class="conversion-btn bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md font-medium transition-colors" data-type="snakecase">
                            snake_case
                        </button>
                        <button class="conversion-btn bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md font-medium transition-colors" data-type="kebabcase">
                            kebab-case
                        </button>
                    </div>
                </div>

                <!-- Output Section -->
                <div class="p-6">
                    <div class="flex justify-between items-center mb-2">
                        <label for="output-text" class="block text-sm font-medium text-gray-700">
                            Converted text:
                        </label>
                        <button id="copy-text" class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                            Copy
                        </button>
                    </div>
                    <textarea
                        id="output-text"
                        rows="8"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                        readonly
                        placeholder="Converted text will appear here..."
                    ></textarea>

                    <div class="mt-3 text-sm text-gray-500">
                        <span id="conversion-info">Select a conversion type above to transform your text</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Google Ad Section -->
    <section class="py-8 bg-gray-100">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p class="text-sm text-gray-500 mb-4">Advertisement</p>
                <!-- Google Ad Placeholder -->
                <div class="bg-white border-2 border-dashed border-gray-300 rounded-lg p-8">
                    <p class="text-gray-400">Google Ad Space (728x90)</p>
                    <!-- Replace with actual Google Ad code -->
                    <ins class="adsbygoogle"
                         style="display:inline-block;width:728px;height:90px"
                         data-ad-client="ca-pub-XXXXXXXXXX"
                         data-ad-slot="XXXXXXXXXX"></ins>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-8 text-center">Case Converter Features</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Multiple Case Types</h3>
                        <p class="text-gray-600">Convert to uppercase, lowercase, title case, sentence case, camelCase, PascalCase, snake_case, and kebab-case.</p>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Instant Results</h3>
                        <p class="text-gray-600">Get immediate text conversion with a single click. No waiting, no processing delays.</p>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Easy Copy</h3>
                        <p class="text-gray-600">One-click copy functionality to quickly use your converted text anywhere.</p>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">No Limits</h3>
                        <p class="text-gray-600">Convert unlimited text with no character limits or usage restrictions.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Other Tools Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-8 text-center">Other Useful Tools</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <a href="{{ route('tools.word-counter') }}" class="block p-6 bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">Word Counter</h3>
                            <p class="text-gray-600">Count words and characters in real-time</p>
                        </div>
                    </div>
                </a>

                <a href="{{ route('tools.age-calculator') }}" class="block p-6 bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">Age Calculator</h3>
                            <p class="text-gray-600">Calculate precise age from birth date</p>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
    @verbatim
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const inputText = document.getElementById('input-text');
                const outputText = document.getElementById('output-text');
                const conversionInfo = document.getElementById('conversion-info');
                const copyButton = document.getElementById('copy-text');
                const clearButton = document.getElementById('clear-text');
                const conversionButtons = document.querySelectorAll('.conversion-btn');

                // Case conversion functions
                const conversions = {
                    uppercase: (text) => text.toUpperCase(),
                    lowercase: (text) => text.toLowerCase(),
                    titlecase: (text) => text.replace(/\w\S*/g, (txt) =>
                        txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
                    ),
                    sentencecase: (text) => text.charAt(0).toUpperCase() + text.slice(1).toLowerCase(),
                    camelcase: (text) => text.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) =>
                        index === 0 ? word.toLowerCase() : word.toUpperCase()
                    ).replace(/\s+/g, ''),
                    pascalcase: (text) => text.replace(/(?:^\w|[A-Z]|\b\w)/g, (word) =>
                        word.toUpperCase()
                    ).replace(/\s+/g, ''),
                    snakecase: (text) => text.toLowerCase().replace(/\s+/g, '_'),
                    kebabcase: (text) => text.toLowerCase().replace(/\s+/g, '-')
                };

                // Add click event to conversion buttons
                conversionButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const type = this.getAttribute('data-type');
                        const input = inputText.value.trim();

                        if (!input) {
                            alert('Please enter some text to convert.');
                            inputText.focus();
                            return;
                        }

                        const converted = conversions[type](input);
                        outputText.value = converted;
                        conversionInfo.textContent = `Converted to ${this.textContent}`;

                        // Visual feedback
                        this.classList.add('ring-2', 'ring-white', 'ring-opacity-50');
                        setTimeout(() => {
                            this.classList.remove('ring-2', 'ring-white', 'ring-opacity-50');
                        }, 200);
                    });
                });

                // Copy functionality
                copyButton.addEventListener('click', function() {
                    if (!outputText.value) {
                        alert('No text to copy. Please convert some text first.');
                        return;
                    }

                    outputText.select();
                    document.execCommand('copy');

                    // Visual feedback
                    const originalText = this.innerHTML;
                    this.innerHTML = '<svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>Copied!';
                    this.classList.add('text-green-600', 'border-green-300');

                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.classList.remove('text-green-600', 'border-green-300');
                    }, 2000);
                });

                // Clear functionality
                clearButton.addEventListener('click', function() {
                    inputText.value = '';
                    outputText.value = '';
                    conversionInfo.textContent = 'Select a conversion type above to transform your text';
                    inputText.focus();
                });

                // Auto-focus on input
                inputText.focus();
            });
        </script>
    @endverbatim
@endpush
