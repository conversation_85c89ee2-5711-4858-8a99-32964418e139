@extends('layouts.app')

@section('title', $tool['name'] . ' - Free Online Word & Character Counter Tool - Unitlly')
@section('meta_description', $tool['meta_description'])
@section('keywords', $tool['keywords'])

@section('content')
<!-- Tool Header -->
<section class="bg-gradient-to-br from-green-50 to-emerald-100 py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mx-auto mb-6">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
        </div>
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">{{ $tool['name'] }}</h1>
        <p class="text-xl text-gray-600 max-w-2xl mx-auto">{{ $tool['description'] }}</p>
    </div>
</section>

<!-- Tool Interface -->
<section class="py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <!-- Statistics Cards -->
            <div class="p-6 bg-gray-50 border-b border-gray-200">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                        <div class="text-2xl font-bold text-blue-600" id="word-count">0</div>
                        <div class="text-sm text-gray-600">Words</div>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                        <div class="text-2xl font-bold text-green-600" id="char-count">0</div>
                        <div class="text-sm text-gray-600">Characters</div>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                        <div class="text-2xl font-bold text-purple-600" id="char-count-no-spaces">0</div>
                        <div class="text-sm text-gray-600">Characters (no spaces)</div>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                        <div class="text-2xl font-bold text-red-600" id="sentence-count">0</div>
                        <div class="text-sm text-gray-600">Sentences</div>
                    </div>
                </div>
            </div>

            <!-- Input Section -->
            <div class="p-6">
                <label for="input-text" class="block text-sm font-medium text-gray-700 mb-2">
                    Type or paste your text below:
                </label>
                <textarea 
                    id="input-text" 
                    rows="12" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 resize-none"
                    placeholder="Start typing here and watch the statistics update in real-time..."
                ></textarea>
                
                <div class="flex justify-between items-center mt-3">
                    <div class="text-sm text-gray-500">
                        <span>Paragraphs: <span id="paragraph-count" class="font-medium">0</span></span>
                        <span class="mx-2">•</span>
                        <span>Reading time: <span id="reading-time" class="font-medium">0 min</span></span>
                        <span class="mx-2">•</span>
                        <span>Speaking time: <span id="speaking-time" class="font-medium">0 min</span></span>
                    </div>
                    <div class="flex space-x-2">
                        <button id="clear-text" class="text-red-600 hover:text-red-700 font-medium text-sm">Clear</button>
                        <button id="copy-text" class="text-blue-600 hover:text-blue-700 font-medium text-sm">Copy Text</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Statistics -->
        <div class="mt-8 bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Detailed Statistics</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="space-y-3">
                        <h4 class="font-medium text-gray-900">Text Analysis</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Average words per sentence:</span>
                                <span id="avg-words-per-sentence" class="font-medium">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Average characters per word:</span>
                                <span id="avg-chars-per-word" class="font-medium">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Longest word:</span>
                                <span id="longest-word" class="font-medium">-</span>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <h4 class="font-medium text-gray-900">Reading Metrics</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Reading time (slow):</span>
                                <span id="reading-time-slow" class="font-medium">0 min</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Reading time (average):</span>
                                <span id="reading-time-avg" class="font-medium">0 min</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Reading time (fast):</span>
                                <span id="reading-time-fast" class="font-medium">0 min</span>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <h4 class="font-medium text-gray-900">Character Breakdown</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Letters:</span>
                                <span id="letter-count" class="font-medium">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Numbers:</span>
                                <span id="number-count" class="font-medium">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Spaces:</span>
                                <span id="space-count" class="font-medium">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Google Ad Section -->
<section class="py-8 bg-gray-100">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <p class="text-sm text-gray-500 mb-4">Advertisement</p>
            <!-- Google Ad Placeholder -->
            <div class="bg-white border-2 border-dashed border-gray-300 rounded-lg p-8">
                <p class="text-gray-400">Google Ad Space (728x90)</p>
                <!-- Replace with actual Google Ad code -->
                <ins class="adsbygoogle"
                     style="display:inline-block;width:728px;height:90px"
                     data-ad-client="ca-pub-XXXXXXXXXX"
                     data-ad-slot="XXXXXXXXXX"></ins>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-16 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-8 text-center">Word Counter Features</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900">Real-time Counting</h3>
                    <p class="text-gray-600">Watch your word and character counts update instantly as you type or paste text.</p>
                </div>
            </div>

            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900">Detailed Analytics</h3>
                    <p class="text-gray-600">Get comprehensive text statistics including reading time, sentence analysis, and character breakdown.</p>
                </div>
            </div>

            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900">Reading Time Estimates</h3>
                    <p class="text-gray-600">Calculate estimated reading and speaking times for different reading speeds.</p>
                </div>
            </div>

            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900">No Character Limits</h3>
                    <p class="text-gray-600">Count unlimited text with no restrictions on document size or content length.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Other Tools Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-8 text-center">Other Useful Tools</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <a href="{{ route('tools.case-converter') }}" class="block p-6 bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Case Converter</h3>
                        <p class="text-gray-600">Convert text between different case formats</p>
                    </div>
                </div>
            </a>

            <a href="{{ route('tools.age-calculator') }}" class="block p-6 bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Age Calculator</h3>
                        <p class="text-gray-600">Calculate precise age from birth date</p>
                    </div>
                </div>
            </a>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const inputText = document.getElementById('input-text');
    const copyButton = document.getElementById('copy-text');
    const clearButton = document.getElementById('clear-text');

    // Statistics elements
    const wordCount = document.getElementById('word-count');
    const charCount = document.getElementById('char-count');
    const charCountNoSpaces = document.getElementById('char-count-no-spaces');
    const sentenceCount = document.getElementById('sentence-count');
    const paragraphCount = document.getElementById('paragraph-count');
    const readingTime = document.getElementById('reading-time');
    const speakingTime = document.getElementById('speaking-time');
    
    // Detailed statistics elements
    const avgWordsPerSentence = document.getElementById('avg-words-per-sentence');
    const avgCharsPerWord = document.getElementById('avg-chars-per-word');
    const longestWord = document.getElementById('longest-word');
    const readingTimeSlow = document.getElementById('reading-time-slow');
    const readingTimeAvg = document.getElementById('reading-time-avg');
    const readingTimeFast = document.getElementById('reading-time-fast');
    const letterCount = document.getElementById('letter-count');
    const numberCount = document.getElementById('number-count');
    const spaceCount = document.getElementById('space-count');

    // Update statistics function
    function updateStatistics() {
        const text = inputText.value;
        
        // Basic counts
        const words = text.trim() === '' ? [] : text.trim().split(/\s+/);
        const characters = text.length;
        const charactersNoSpaces = text.replace(/\s/g, '').length;
        const sentences = text.trim() === '' ? [] : text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const paragraphs = text.trim() === '' ? [] : text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
        
        // Update basic statistics
        wordCount.textContent = words.length;
        charCount.textContent = characters;
        charCountNoSpaces.textContent = charactersNoSpaces;
        sentenceCount.textContent = sentences.length;
        paragraphCount.textContent = paragraphs.length;
        
        // Reading time calculations (words per minute)
        const wordsPerMinuteSlow = 150;
        const wordsPerMinuteAvg = 200;
        const wordsPerMinuteFast = 250;
        const wordsPerMinuteSpeaking = 130;
        
        const readingMinutes = Math.ceil(words.length / wordsPerMinuteAvg);
        const speakingMinutes = Math.ceil(words.length / wordsPerMinuteSpeaking);
        
        readingTime.textContent = readingMinutes + ' min';
        speakingTime.textContent = speakingMinutes + ' min';
        
        // Detailed statistics
        const avgWordsPerSent = sentences.length > 0 ? Math.round(words.length / sentences.length * 10) / 10 : 0;
        const avgCharsPerWordVal = words.length > 0 ? Math.round(charactersNoSpaces / words.length * 10) / 10 : 0;
        const longestWordVal = words.length > 0 ? words.reduce((a, b) => a.length > b.length ? a : b) : '-';
        
        avgWordsPerSentence.textContent = avgWordsPerSent;
        avgCharsPerWord.textContent = avgCharsPerWordVal;
        longestWord.textContent = longestWordVal;
        
        // Reading times for different speeds
        readingTimeSlow.textContent = Math.ceil(words.length / wordsPerMinuteSlow) + ' min';
        readingTimeAvg.textContent = Math.ceil(words.length / wordsPerMinuteAvg) + ' min';
        readingTimeFast.textContent = Math.ceil(words.length / wordsPerMinuteFast) + ' min';
        
        // Character breakdown
        const letters = (text.match(/[a-zA-Z]/g) || []).length;
        const numbers = (text.match(/[0-9]/g) || []).length;
        const spaces = (text.match(/\s/g) || []).length;
        
        letterCount.textContent = letters;
        numberCount.textContent = numbers;
        spaceCount.textContent = spaces;
    }

    // Real-time updates
    inputText.addEventListener('input', updateStatistics);
    inputText.addEventListener('paste', function() {
        setTimeout(updateStatistics, 10);
    });

    // Copy functionality
    copyButton.addEventListener('click', function() {
        if (!inputText.value.trim()) {
            alert('No text to copy. Please enter some text first.');
            return;
        }

        inputText.select();
        document.execCommand('copy');
        
        // Visual feedback
        const originalText = this.textContent;
        this.textContent = 'Copied!';
        this.classList.add('text-green-600');
        
        setTimeout(() => {
            this.textContent = originalText;
            this.classList.remove('text-green-600');
        }, 2000);
    });

    // Clear functionality
    clearButton.addEventListener('click', function() {
        inputText.value = '';
        updateStatistics();
        inputText.focus();
    });

    // Auto-focus on input
    inputText.focus();
    
    // Initial update
    updateStatistics();
});
</script>
@endpush
