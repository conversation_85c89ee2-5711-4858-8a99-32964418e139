@extends('layouts.app')

@section('title', 'About Unitlly - Free Online Tools for Everyone')
@section('meta_description', 'Learn about <PERSON><PERSON>\'s mission to provide free, fast, and secure online tools for text processing, calculations, and productivity.')

@section('content')
<!-- Header -->
<section class="bg-gradient-to-br from-blue-50 to-indigo-100 py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">About Unitlly</h1>
        <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Empowering productivity with free, fast, and secure online tools for everyone.
        </p>
    </div>
</section>

<!-- Mission Section -->
<section class="py-16 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Our Mission</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                To provide everyone with access to powerful, easy-to-use online tools that enhance productivity 
                and simplify daily tasks - completely free and without compromise.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Privacy First</h3>
                <p class="text-gray-600">All processing happens in your browser. Your data never leaves your device, ensuring complete privacy and security.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Lightning Fast</h3>
                <p class="text-gray-600">Optimized for speed with instant results. No waiting, no delays - just immediate, accurate processing.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Always Free</h3>
                <p class="text-gray-600">No registration, no subscriptions, no hidden fees. Quality tools accessible to everyone, everywhere.</p>
            </div>
        </div>
    </div>
</section>

<!-- Story Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
            <div>
                <h2 class="text-3xl font-bold text-gray-900 mb-6">Our Story</h2>
                <div class="space-y-4 text-gray-600">
                    <p>
                        Unitlly was born from a simple observation: people need quick, reliable tools for everyday tasks, 
                        but most online tools are cluttered with ads, require registration, or compromise on privacy.
                    </p>
                    <p>
                        We set out to create something different - a collection of clean, fast, and secure tools that 
                        respect your privacy while delivering exceptional functionality. Every tool is designed with 
                        user experience at the forefront, ensuring they work seamlessly across all devices.
                    </p>
                    <p>
                        Today, thousands of users rely on Unitlly for their daily text processing, calculations, and 
                        productivity needs. We're proud to offer these tools completely free, with no strings attached.
                    </p>
                </div>
            </div>
            <div class="mt-12 lg:mt-0">
                <div class="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8">
                    <div class="grid grid-cols-2 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-blue-600 mb-2">10K+</div>
                            <div class="text-sm text-gray-600">Monthly Users</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-green-600 mb-2">100%</div>
                            <div class="text-sm text-gray-600">Uptime</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-purple-600 mb-2">3</div>
                            <div class="text-sm text-gray-600">Core Tools</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-red-600 mb-2">0</div>
                            <div class="text-sm text-gray-600">Data Stored</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Values Section -->
<section class="py-16 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Our Values</h2>
            <p class="text-xl text-gray-600">The principles that guide everything we do</p>
        </div>

        <div class="space-y-8">
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Simplicity</h3>
                    <p class="text-gray-600">
                        We believe great tools should be intuitive and easy to use. No complex interfaces, 
                        no unnecessary features - just clean, focused functionality that gets the job done.
                    </p>
                </div>
            </div>

            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Privacy</h3>
                    <p class="text-gray-600">
                        Your data is yours. We don't collect, store, or transmit your personal information. 
                        All processing happens locally in your browser, ensuring your privacy is always protected.
                    </p>
                </div>
            </div>

            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Performance</h3>
                    <p class="text-gray-600">
                        Speed matters. Our tools are optimized for performance, delivering instant results 
                        without compromising on accuracy or functionality.
                    </p>
                </div>
            </div>

            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Accessibility</h3>
                    <p class="text-gray-600">
                        Everyone deserves access to quality tools. We're committed to keeping our tools free, 
                        accessible, and available to users worldwide, regardless of their circumstances.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Tools Overview -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Our Tools</h2>
            <p class="text-xl text-gray-600">Carefully crafted tools for your everyday needs</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Case Converter</h3>
                <p class="text-gray-600 mb-4">Transform text between different case formats instantly. Perfect for formatting content for different purposes.</p>
                <a href="{{ route('tools.case-converter') }}" class="text-blue-600 hover:text-blue-700 font-medium">Try it now →</a>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Word Counter</h3>
                <p class="text-gray-600 mb-4">Count words, characters, and get detailed text statistics in real-time as you type or paste content.</p>
                <a href="{{ route('tools.word-counter') }}" class="text-green-600 hover:text-green-700 font-medium">Try it now →</a>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Age Calculator</h3>
                <p class="text-gray-600 mb-4">Calculate your exact age in years, months, and days. Find out interesting facts about your birth date.</p>
                <a href="{{ route('tools.age-calculator') }}" class="text-purple-600 hover:text-purple-700 font-medium">Try it now →</a>
            </div>
        </div>
    </div>
</section>

<!-- Future Plans -->
<section class="py-16 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">What's Next?</h2>
            <p class="text-xl text-gray-600">We're constantly working to improve and expand our tool collection</p>
        </div>

        <div class="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8">
            <div class="text-center">
                <h3 class="text-2xl font-bold text-gray-900 mb-4">Coming Soon</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-blue-600 rounded-full"></div>
                        <span class="text-gray-700">URL Encoder/Decoder</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-green-600 rounded-full"></div>
                        <span class="text-gray-700">Password Generator</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-purple-600 rounded-full"></div>
                        <span class="text-gray-700">QR Code Generator</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-red-600 rounded-full"></div>
                        <span class="text-gray-700">Color Palette Generator</span>
                    </div>
                </div>
                <div class="mt-8">
                    <a href="{{ route('contact') }}" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-colors">
                        Suggest a Tool
                        <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
