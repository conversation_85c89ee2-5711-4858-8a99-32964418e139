<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Free Online Text & Utility Tools - Unitlly')</title>
    <meta name="description" content="@yield('meta_description', 'Free online tools for text processing, case conversion, word counting, age calculation and more. Fast, secure, and user-friendly utilities.')">
    <meta name="keywords" content="@yield('keywords', 'online tools, text tools, case converter, word counter, age calculator, free utilities')">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:title" content="@yield('title', 'Free Online Text & Utility Tools - Unitlly')">
    <meta property="og:description" content="@yield('meta_description', 'Free online tools for text processing, case conversion, word counting, age calculation and more.')">
    <meta property="og:image" content="{{ asset('images/og-image.jpg') }}">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ url()->current() }}">
    <meta property="twitter:title" content="@yield('title', 'Free Online Text & Utility Tools - Unitlly')">
    <meta property="twitter:description" content="@yield('meta_description', 'Free online tools for text processing, case conversion, word counting, age calculation and more.')">
    <meta property="twitter:image" content="{{ asset('images/og-image.jpg') }}">

    <!-- Canonical URL -->
    <link rel="canonical" href="{{ url()->current() }}">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Unitlly",
        "description": "Free online tools for text processing, case conversion, word counting, age calculation and more.",
        "url": "{{ url('/') }}",
        "potentialAction": {
            "@type": "SearchAction",
            "target": {
                "@type": "EntryPoint",
                "urlTemplate": "{{ url('/') }}?q={search_term_string}"
            },
            "query-input": "required name=search_term_string"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Unitlly",
            "url": "{{ url('/') }}",
            "logo": {
                "@type": "ImageObject",
                "url": "{{ asset('images/logo.png') }}"
            }
        }
    }
    </script>

    @if(request()->routeIs('tools.*'))
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "@yield('title')",
        "description": "@yield('meta_description')",
        "url": "{{ url()->current() }}",
        "applicationCategory": "UtilitiesApplication",
        "operatingSystem": "Any",
        "permissions": "browser",
        "isAccessibleForFree": true,
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        }
    }
    </script>
    @endif

    <!-- Performance optimizations -->
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link rel="preconnect" href="https://fonts.bunny.net" crossorigin>

    <!-- Google AdSense (Replace with your actual AdSense code) -->
{{--    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXX" crossorigin="anonymous"></script>--}}

    @stack('head')
</head>
<body class="bg-gray-50 text-gray-900 font-sans antialiased">
    <!-- Skip to main content -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>

    <!-- Header -->
    <header class="bg-white shadow-sm sticky top-0 z-40">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="{{ route('home') }}" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">U</span>
                        </div>
                        <span class="text-xl font-bold text-gray-900">Unitlly</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="{{ route('home') }}" class="@if(request()->routeIs('home')) text-blue-600 border-b-2 border-blue-600 @else text-gray-700 hover:text-blue-600 @endif px-3 py-2 text-sm font-medium transition-colors">
                            Home
                        </a>

                        <!-- Tools Dropdown -->
                        <div class="relative group">
                            <button class="@if(request()->routeIs('tools.*')) text-blue-600 @else text-gray-700 hover:text-blue-600 @endif px-3 py-2 text-sm font-medium transition-colors flex items-center">
                                Tools
                                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                                <div class="py-1">
                                    <a href="{{ route('tools.case-converter') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <div class="font-medium">Case Converter</div>
                                        <div class="text-xs text-gray-500">Convert text case formats</div>
                                    </a>
                                    <a href="{{ route('tools.word-counter') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <div class="font-medium">Word Counter</div>
                                        <div class="text-xs text-gray-500">Count words and characters</div>
                                    </a>
                                    <a href="{{ route('tools.age-calculator') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <div class="font-medium">Age Calculator</div>
                                        <div class="text-xs text-gray-500">Calculate precise age</div>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <a href="{{ route('about') }}" class="@if(request()->routeIs('about')) text-blue-600 border-b-2 border-blue-600 @else text-gray-700 hover:text-blue-600 @endif px-3 py-2 text-sm font-medium transition-colors">
                            About
                        </a>
                        <a href="{{ route('contact') }}" class="@if(request()->routeIs('contact')) text-blue-600 border-b-2 border-blue-600 @else text-gray-700 hover:text-blue-600 @endif px-3 py-2 text-sm font-medium transition-colors">
                            Contact
                        </a>
                    </div>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="mobile-menu-button inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500">
                        <span class="sr-only">Open main menu</span>
                        <svg class="block h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Mobile Navigation -->
            <div class="mobile-menu hidden md:hidden">
                <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
                    <a href="{{ route('home') }}" class="@if(request()->routeIs('home')) text-blue-600 bg-blue-50 @else text-gray-700 hover:text-blue-600 hover:bg-gray-50 @endif block px-3 py-2 text-base font-medium rounded-md">
                        Home
                    </a>
                    <div class="space-y-1">
                        <div class="text-gray-500 px-3 py-2 text-sm font-medium">Tools</div>
                        <a href="{{ route('tools.case-converter') }}" class="@if(request()->routeIs('tools.case-converter')) text-blue-600 bg-blue-50 @else text-gray-700 hover:text-blue-600 hover:bg-gray-50 @endif block px-6 py-2 text-sm rounded-md">
                            Case Converter
                        </a>
                        <a href="{{ route('tools.word-counter') }}" class="@if(request()->routeIs('tools.word-counter')) text-blue-600 bg-blue-50 @else text-gray-700 hover:text-blue-600 hover:bg-gray-50 @endif block px-6 py-2 text-sm rounded-md">
                            Word Counter
                        </a>
                        <a href="{{ route('tools.age-calculator') }}" class="@if(request()->routeIs('tools.age-calculator')) text-blue-600 bg-blue-50 @else text-gray-700 hover:text-blue-600 hover:bg-gray-50 @endif block px-6 py-2 text-sm rounded-md">
                            Age Calculator
                        </a>
                    </div>
                    <a href="{{ route('about') }}" class="@if(request()->routeIs('about')) text-blue-600 bg-blue-50 @else text-gray-700 hover:text-blue-600 hover:bg-gray-50 @endif block px-3 py-2 text-base font-medium rounded-md">
                        About
                    </a>
                    <a href="{{ route('contact') }}" class="@if(request()->routeIs('contact')) text-blue-600 bg-blue-50 @else text-gray-700 hover:text-blue-600 hover:bg-gray-50 @endif block px-3 py-2 text-base font-medium rounded-md">
                        Contact
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main id="main-content" class="min-h-screen">
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Brand -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">U</span>
                        </div>
                        <span class="text-xl font-bold">Unitlly</span>
                    </div>
                    <p class="text-gray-300 mb-4 max-w-md">
                        Free online tools for text processing, case conversion, word counting, age calculation and more.
                        Fast, secure, and user-friendly utilities for everyone.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <span class="sr-only">Twitter</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <span class="sr-only">GitHub</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clip-rule="evenodd"></path>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase mb-4">Tools</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('tools.case-converter') }}" class="text-gray-300 hover:text-white transition-colors">Case Converter</a></li>
                        <li><a href="{{ route('tools.word-counter') }}" class="text-gray-300 hover:text-white transition-colors">Word Counter</a></li>
                        <li><a href="{{ route('tools.age-calculator') }}" class="text-gray-300 hover:text-white transition-colors">Age Calculator</a></li>
                    </ul>
                </div>

                <!-- Company -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase mb-4">Company</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('about') }}" class="text-gray-300 hover:text-white transition-colors">About</a></li>
                        <li><a href="{{ route('contact') }}" class="text-gray-300 hover:text-white transition-colors">Contact</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Privacy Policy</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="mt-8 pt-8 border-t border-gray-800">
                <p class="text-center text-gray-400 text-sm">
                    © {{ date('Y') }} Unitlly. All rights reserved. Made with ❤️ for productivity.
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script>
        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuButton = document.querySelector('.mobile-menu-button');
            const mobileMenu = document.querySelector('.mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }
        });
    </script>

    @stack('scripts')
</body>
</html>
